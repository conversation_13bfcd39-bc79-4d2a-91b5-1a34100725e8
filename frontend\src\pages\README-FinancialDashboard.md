# Financial Dashboard

A comprehensive React component for displaying financial data from the Financial API.

## Features

### 📊 **Data Sections**
1. **Summary Cards** - Overview metrics with icons
2. **Chart of Accounts** - First 2 accounts from Profit & Loss
3. **Profit & Loss Data** - With account names bound to each record
4. **Trial Balance Data** - With account names and debit/credit amounts
5. **Balance Sheet Data** - With account names and statement amounts
6. **AR Aging** - Full company-wide accounts receivable data
7. **AP Aging** - Full company-wide accounts payable data
8. **Data Consistency Report** - Validation and sync status

### 🎨 **UI Features**
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Color-coded Sections** - Each financial section has distinct colors
- **Interactive Tables** - Hover effects and proper formatting
- **Loading States** - Spinner while fetching data
- **Error Handling** - User-friendly error messages
- **Currency Formatting** - Proper USD formatting for all amounts
- **Date Formatting** - Human-readable date display

### 🔧 **Functionality**
- **Company Selection** - Input field to change company ID
- **Auto-refresh** - Fetches data on component mount
- **Manual Refresh** - Button to reload data
- **Data Validation** - Shows which accounts have complete data
- **Sync Status** - Displays last sync dates for each data type

## Usage

### Basic Usage
```jsx
import FinancialDashboard from './pages/FinancialDashboard';

function App() {
  return <FinancialDashboard />;
}
```

### With Routing
```jsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import FinancialDashboard from './pages/FinancialDashboard';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/financial" element={<FinancialDashboard />} />
      </Routes>
    </BrowserRouter>
  );
}
```

## API Integration

The dashboard uses the `getComprehensiveFinancialData` service function:

```javascript
// Fetches data for company ID 1
const response = await getComprehensiveFinancialData(1);

// Response structure:
{
  success: true,
  statusCode: 200,
  data: {
    company: { id, name, qboConnectionStatus },
    summary: { totalAccounts, dataConsistency, etc. },
    chartOfAccounts: [...],
    profitLoss: [...],
    trialBalance: [...],
    balanceSheet: [...],
    arAging: [...],
    apAging: [...],
    accountLookup: {...}
  }
}
```

## Component Structure

### State Management
- `financialData` - Stores the API response data
- `loading` - Boolean for loading state
- `error` - Error message string
- `selectedCompanyId` - Currently selected company ID

### Key Functions
- `fetchFinancialData()` - Calls the API and updates state
- `formatCurrency()` - Formats numbers as USD currency
- `formatDate()` - Formats ISO dates to readable format

## Styling

### CSS Classes Used
- **Tailwind CSS** - For responsive layout and utilities
- **Custom CSS** - For hover effects and table styling
- **Color Scheme**:
  - Green: Profit & Loss section
  - Purple: Trial Balance section
  - Orange: Balance Sheet section
  - Blue: AR Aging section
  - Red: AP Aging section

### Responsive Breakpoints
- **Mobile** (< 768px): Single column layout
- **Tablet** (768px - 1024px): 2-column grid for cards
- **Desktop** (> 1024px): 4-column grid for cards

## Data Display

### Summary Cards
- Total Accounts
- P&L Records Count
- Trial Balance Records Count
- Balance Sheet Records Count

### Financial Tables
Each table shows:
- Account ID and Name (where applicable)
- Year and Month
- Financial amounts (properly formatted)
- Account type and classification

### AR/AP Aging
- Company-wide data (not filtered by accounts)
- Year, Month, Total Amount
- Last updated timestamps

### Data Consistency
- Visual indicators (✓/✗) for data completeness
- Account selection method display
- Last sync dates for each data type

## Error Handling

- **Network Errors** - Shows retry button
- **API Errors** - Displays error message from API
- **No Data** - Shows appropriate empty state messages
- **Loading States** - Spinner with descriptive text

## Accessibility

- **Keyboard Navigation** - All interactive elements are focusable
- **Screen Reader Support** - Proper ARIA labels and semantic HTML
- **Color Contrast** - Meets WCAG guidelines
- **Responsive Text** - Scales appropriately on different devices

## Dependencies

Required packages:
```json
{
  "react": "^18.0.0",
  "lucide-react": "^0.263.1"
}
```

Optional (for routing):
```json
{
  "react-router-dom": "^6.0.0"
}
```

## File Structure

```
frontend/src/
├── pages/
│   ├── FinancialDashboard.jsx     # Main dashboard component
│   └── README-FinancialDashboard.md
├── components/ui/
│   ├── card.jsx                   # Card components
│   ├── button.jsx                 # Button component
│   └── alert.jsx                  # Alert components
├── services/
│   └── financial.js               # API service functions
├── styles/
│   └── financial-dashboard.css    # Custom styles
└── routes/
    └── FinancialRoutes.jsx        # Route definitions
```

## Customization

### Adding New Sections
1. Add new data to the API response
2. Create a new Card component in the dashboard
3. Add appropriate styling and formatting
4. Update the summary statistics

### Modifying Table Columns
1. Update the table headers in the JSX
2. Add corresponding data mapping in the table body
3. Ensure proper formatting for new data types

### Changing Colors
1. Update the CSS classes in `financial-dashboard.css`
2. Modify the `bg-{color}-50` classes in the component
3. Update hover effects accordingly
