/*
  Warnings:

  - A unique constraint covering the columns `[accountId,userId,realmId]` on the table `Account` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month,accountId]` on the table `BalanceSheetReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month,accountId]` on the table `ProfitLossReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month,accountId]` on the table `TrialBalanceReport` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "Account_accountId_userId_realmId_key" ON "Account"("accountId", "userId", "realmId");

-- CreateIndex
CREATE UNIQUE INDEX "BalanceSheetReport_userId_realmId_year_month_accountId_key" ON "BalanceSheetReport"("userId", "realmId", "year", "month", "accountId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfitLossReport_userId_realmId_year_month_accountId_key" ON "ProfitLossReport"("userId", "realmId", "year", "month", "accountId");

-- CreateIndex
CREATE UNIQUE INDEX "TrialBalanceReport_userId_realmId_year_month_accountId_key" ON "TrialBalanceReport"("userId", "realmId", "year", "month", "accountId");

-- AddForeignKey
ALTER TABLE "BalanceSheetReport" ADD CONSTRAINT "BalanceSheetReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfitLossReport" ADD CONSTRAINT "ProfitLossReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrialBalanceReport" ADD CONSTRAINT "TrialBalanceReport_accountId_userId_realmId_fkey" FOREIGN KEY ("accountId", "userId", "realmId") REFERENCES "Account"("accountId", "userId", "realmId") ON DELETE RESTRICT ON UPDATE CASCADE;
