import { Router } from 'express';
import * as pdfController from '../controllers/pdf.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

export const pdfRoute = Router();

// Generate PDF from report components
pdfRoute.post('/generate', pdfController.generateReportPDF);

// Generate PDF from URL (for frontend rendering)
pdfRoute.post('/generate-from-url', pdfController.generatePDFFromURL);

// Generate PDF and store in S3
pdfRoute.post('/generate-and-store', pdfController.generateAndStorePDF);
