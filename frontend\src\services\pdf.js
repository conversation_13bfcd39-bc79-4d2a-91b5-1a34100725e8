import axiosInstance from "./axiosInstance";

/**
 * Generate PDF from report components
 * @param {Object} options - PDF generation options
 * @param {Object} options.templateSettings - Template styling settings
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @returns {Promise<Blob>} - PDF blob for download
 */
export const generateReportPDF = async (options) => {
  try {
    const {
      templateSettings,
      companyName = 'Company Report',
      reportTitle = 'Financial Report'
    } = options;

    const response = await axiosInstance.post('/pdf/generate', {
      templateSettings,
      companyName,
      reportTitle
    }, {
      responseType: 'blob', // Important for file downloads
      timeout: 60000 // 60 seconds timeout for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(
      error.response?.data?.message || 
      'Failed to generate PDF. Please try again.'
    );
  }
};

/**
 * Generate PDF from URL
 * @param {Object} options - PDF generation options
 * @param {string} options.url - URL to render as PDF
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @param {string} options.selector - CSS selector to wait for
 * @returns {Promise<Blob>} - PDF blob for download
 */
export const generatePDFFromURL = async (options) => {
  try {
    const {
      url,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      selector = '.report-container'
    } = options;

    const response = await axiosInstance.post('/pdf/generate-from-url', {
      url,
      companyName,
      reportTitle,
      selector
    }, {
      responseType: 'blob', // Important for file downloads
      timeout: 60000 // 60 seconds timeout for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating PDF from URL:', error);
    throw new Error(
      error.response?.data?.message || 
      'Failed to generate PDF from URL. Please try again.'
    );
  }
};

/**
 * Generate PDF and store in S3
 * @param {Object} options - PDF generation options
 * @param {Object} options.templateSettings - Template styling settings
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @returns {Promise<Object>} - S3 file information
 */
export const generateAndStorePDF = async (options) => {
  try {
    const {
      templateSettings,
      companyName = 'Company Report',
      reportTitle = 'Financial Report'
    } = options;

    const response = await axiosInstance.post('/pdf/generate-and-store', {
      templateSettings,
      companyName,
      reportTitle
    }, {
      timeout: 60000 // 60 seconds timeout for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating and storing PDF:', error);
    throw new Error(
      error.response?.data?.message || 
      'Failed to generate and store PDF. Please try again.'
    );
  }
};

/**
 * Download blob as file
 * @param {Blob} blob - File blob
 * @param {string} filename - Filename for download
 */
export const downloadBlob = (blob, filename) => {
  try {
    // Create blob URL
    const url = window.URL.createObjectURL(blob);
    
    // Create temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    
    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up blob URL
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw new Error('Failed to download file. Please try again.');
  }
};

/**
 * Generate filename for PDF
 * @param {string} companyName - Company name
 * @param {string} reportTitle - Report title
 * @returns {string} - Generated filename
 */
export const generatePDFFilename = (companyName, reportTitle) => {
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const sanitizedCompany = companyName.replace(/[^a-zA-Z0-9]/g, '-');
  const sanitizedTitle = reportTitle.replace(/[^a-zA-Z0-9]/g, '-');
  
  return `${sanitizedCompany}-${sanitizedTitle}-${timestamp}.pdf`;
};
