import { pdfGenerationService } from '../services/pdfGeneration.service.js';
import { uploadFileOnS3 } from '../utils/aws-s3.js';
import { generatePDFUrl } from '../utils/reportTemplateGenerator.js';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate PDF from report components using frontend URL
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export const generateReportPDF = async (req, res) => {
  try {
    const {
      templateSettings,
      companyName = 'Company Report',
      reportTitle = 'Financial Report'
    } = req.body;

    // Get frontend URL from environment or use default
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

    // Generate PDF URL with template settings
    const pdfUrl = generatePDFUrl(frontendUrl, templateSettings);

    // Generate PDF from URL
    const pdfBuffer = await pdfGenerationService.generatePDFFromURL({
      url: pdfUrl,
      companyName,
      reportTitle,
      selector: '.report-container'
    });

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${companyName.replace(/\s+/g, '-')}-${reportTitle.replace(/\s+/g, '-')}-${timestamp}.pdf`;

    // Save PDF to temporary file
    const tempDir = path.join(process.cwd(), 'temp');
    const filePath = await pdfGenerationService.savePDFToFile(pdfBuffer, filename, tempDir);

    // Return file for download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Clean up temp file after sending
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error in generateReportPDF controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF',
      error: error.message
    });
  }
};

/**
 * Generate PDF from URL (for frontend rendering)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export const generatePDFFromURL = async (req, res) => {
  try {
    const {
      url,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      selector
    } = req.body;

    console.log('PDF generation request:', { url, companyName, reportTitle, selector });

    // Validate required fields
    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Generate PDF
    console.log('Starting PDF generation...');
    const pdfBuffer = await pdfGenerationService.generatePDFFromURL({
      url,
      companyName,
      reportTitle,
      selector
    });
    console.log('PDF generation completed successfully');

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${companyName.replace(/\s+/g, '-')}-${reportTitle.replace(/\s+/g, '-')}-${timestamp}.pdf`;

    // Save PDF to temporary file
    const tempDir = path.join(process.cwd(), 'temp');
    const filePath = await pdfGenerationService.savePDFToFile(pdfBuffer, filename, tempDir);

    // Return file for download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    // Clean up temp file after sending
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });

  } catch (error) {
    console.error('Error in generatePDFFromURL controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF from URL',
      error: error.message
    });
  }
};

/**
 * Generate PDF and store in S3
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export const generateAndStorePDF = async (req, res) => {
  try {
    const {
      templateSettings,
      components,
      companyName = 'Company Report',
      reportTitle = 'Financial Report'
    } = req.body;

    // Validate required fields
    if (!components) {
      return res.status(400).json({
        success: false,
        message: 'Report components are required'
      });
    }

    // Generate PDF
    const pdfBuffer = await pdfGenerationService.generateReportPDF({
      templateSettings,
      components,
      companyName,
      reportTitle
    });

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${companyName.replace(/\s+/g, '-')}-${reportTitle.replace(/\s+/g, '-')}-${timestamp}.pdf`;

    // Save PDF to temporary file
    const tempDir = path.join(process.cwd(), 'temp');
    const filePath = await pdfGenerationService.savePDFToFile(pdfBuffer, filename, tempDir);

    // Upload to S3
    const s3Key = `reports/${uuidv4()}/${filename}`;
    const s3Result = await uploadFileOnS3(filePath, s3Key);

    // Clean up temp file
    fs.unlinkSync(filePath);

    // Return S3 info
    res.status(200).json({
      success: true,
      message: 'PDF generated and stored successfully',
      data: {
        fileKey: s3Key,
        fileName: filename,
        fileUrl: s3Result.Location
      }
    });

  } catch (error) {
    console.error('Error in generateAndStorePDF controller:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate and store PDF',
      error: error.message
    });
  }
};
