/*
  Warnings:

  - You are about to drop the column `userId` on the `ReportContentSettings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[companyId,reportType]` on the table `ReportContentSettings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `companyId` to the `ReportContentSettings` table without a default value. This is not possible if the table is not empty.

*/

-- Step 1: Add the companyId column as nullable first
ALTER TABLE "ReportContentSettings" ADD COLUMN "companyId" INTEGER;

-- Step 2: Update existing records to map userId to companyId
-- This assumes each user has at least one company they own
UPDATE "ReportContentSettings"
SET "companyId" = (
  SELECT c.id
  FROM "Company" c
  WHERE c."userId" = "ReportContentSettings"."userId"
  LIMIT 1
)
WHERE "companyId" IS NULL;

-- Step 3: Make companyId NOT NULL after data migration
ALTER TABLE "ReportContentSettings" ALTER COLUMN "companyId" SET NOT NULL;

-- Step 4: Drop the userId column
ALTER TABLE "ReportContentSettings" DROP COLUMN "userId";

-- CreateIndex
CREATE INDEX "ReportContentSettings_companyId_idx" ON "ReportContentSettings"("companyId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportContentSettings_companyId_reportType_key" ON "ReportContentSettings"("companyId", "reportType");

-- AddForeignKey
ALTER TABLE "ReportContentSettings" ADD CONSTRAINT "ReportContentSettings_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;
